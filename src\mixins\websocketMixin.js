/**
 * WebSocket处理Mixin
 * 提供WebSocket连接、消息处理、设备状态管理等功能
 */
import { getToken } from "@/utils/auth";
import socketService from '../../socket/index';

export default {
  data() {
    return {
      // WebSocket连接状态
      websocketStatus: 'disconnected', // disconnected, connecting, connected, error
      websocketError: null,
      reconnectCallback: null, // WebSocket重连回调函数引用

      // WebSocket OSD数据
      osdDock: {},

      // 设备最后更新时间跟踪
      deviceLastUpdateTime: {}, // 存储设备最后更新时间 {deviceSn: timestamp}

      // 超时检测定时器
      timeoutCheckTimer: null,

      // 超时时间设置（毫秒）
      DEVICE_TIMEOUT: 5000 // 5秒
    };
  },

  methods: {
    // 新增连接 WebSocket 的方法
    async connectWebSocket() {
      const token = getToken(); // 获取实际的 Authorization token
      console.log("开始连接WebSocket，token:", token);

      this.websocketStatus = 'connecting';
      this.websocketError = null;

      try {
        // 设置消息处理器（在连接前设置，确保重连时也能使用）
        console.log("设置WebSocket消息处理器...");
        this.setupMessageHandler();

        // 添加重连回调
        this.reconnectCallback = () => {
          console.log("WebSocket重连成功，更新状态");
          this.websocketStatus = 'connected';
          this.websocketError = null;
          this.$message.success("WebSocket重连成功");
        };
        socketService.addReconnectCallback(this.reconnectCallback);

        // 初始化WebSocket连接
        console.log("初始化WebSocket连接...");
        socketService.init("warning-all");

        // 等待连接建立
        console.log("等待WebSocket连接建立...");
        await this.waitForConnection();

        this.websocketStatus = 'connected';
        console.log("WebSocket连接和消息处理器设置完成");
        this.$message.success("WebSocket连接成功");
      } catch (error) {
        console.error("WebSocket连接失败:", error);
        this.websocketStatus = 'error';
        this.websocketError = error.message;
        this.$message.error(`WebSocket连接失败: ${error.message}`);
      }
    },

    // 等待WebSocket连接建立
    waitForConnection() {
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (socketService.socket) {
            if (socketService.socket.readyState === WebSocket.OPEN) {
              console.log("WebSocket连接已建立");
              resolve();
            } else if (socketService.socket.readyState === WebSocket.CLOSED ||
                      socketService.socket.readyState === WebSocket.CLOSING) {
              reject(new Error("WebSocket连接失败"));
            } else {
              // 连接中，继续等待
              setTimeout(checkConnection, 100);
            }
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();

        // 设置超时
        setTimeout(() => {
          reject(new Error("WebSocket连接超时"));
        }, 10000);
      });
    },

    // 设置消息处理器
    setupMessageHandler() {
      console.log("设置WebSocket消息处理器");

      // 创建消息处理器函数
      const messageHandler = (event) => {
        try {
          //console.log("收到原始WebSocket消息:", event.data);
          this.handleWebSocketMessage(JSON.parse(event.data));
        } catch (error) {
          console.error("处理WebSocket消息失败:", error, event.data);
        }
      };

      // 使用socketService的新API设置消息处理器
      socketService.setMessageHandler(messageHandler);
      console.log("WebSocket消息处理器已设置");
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      try {
        // 检查是否是dock_osd消息
        if (data && data.biz_code === 'dock_osd') {
          this.handleDockOsdMessage(data);
        }
        // 检查是否是device_osd消息（无人机OSD）
        if (data && data.biz_code === 'device_osd') {
          this.handleDroneOsdMessage(data);
        }
        //设备上线
        if (data && data.biz_code === 'device_online') {
          console.log("设备上线了");
          this.$message.success(`设备上线: ${data.sn}`);
        }
        if (data && data.biz_code === 'device_offline') {
          this.handleDeviceOfflineMessage(data);
        }
      } catch (error) {
        console.error('处理WebSocket消息失败:', error, data);
      }
    },

    // 处理dock_osd消息
    handleDockOsdMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.host) {
          const sn = messageData.sn;
          // 如果osdDock中还没有该设备的数据，初始化
          if (!this.osdDock[sn]) {
            this.$set(this.osdDock, sn, {});
          }
          // 合并host数据到osdDock
          this.$set(this.osdDock, sn, {
            ...this.osdDock[sn],
            ...messageData.host,
            lastUpdate: Date.now(), // 添加最后更新时间
            sn: sn // 保存设备SN
          });
          
          // 更新deviceList中对应设备的信息
          this.updateDeviceFromOsd(sn, messageData.host);
          // 更新设备最后更新时间
          this.updateDeviceLastUpdateTime(sn);
        }
      } catch (error) {
        console.error('处理dock_osd消息失败:', error);
      }
    },

    // 从OSD数据更新设备列表信息
    updateDeviceFromOsd(sn, hostData) {
      try {
        if (!this.deviceList || !Array.isArray(this.deviceList)) {
          console.warn('deviceList不存在或不是数组');
          return;
        }
        // 查找对应的设备
        const device = this.deviceList.find(d => d.device_sn === sn);
       
        if (device) {
          // 安全更新mode_code
          if (hostData.mode_code !== undefined && hostData.mode_code !== null) {
            this.$set(device, 'mode_code', hostData.mode_code);
          }
          // 安全更新task_name
          if (hostData.task_name !== undefined && hostData.task_name !== null) {
            this.$set(device, 'task_name', hostData.task_name);
          }
          // 更新其他可能需要的字段
          if (hostData.drc_state !== undefined && hostData.drc_state !== null) {
            this.$set(device, 'drc_state', hostData.drc_state);
          }
        } else {
          console.warn(`未找到SN为 ${sn} 的设备`);
        }
      } catch (error) {
        console.error('更新设备信息失败:', error);
      }
    },

    // 处理device_osd消息（无人机OSD）
    handleDroneOsdMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.host) {
          const droneSn = messageData.sn; // 无人机SN
          const parentSn = messageData.host.parent_sn; // 机场SN
          const droneMode = messageData.host.mode_code; // 无人机mode_code

          console.log('收到无人机OSD消息:', {
            droneSn,
            parentSn,
            droneMode,
            messageData
          });

          // 通过parent_sn找到对应的机场设备，并更新其子设备的mode_code
          const dockDevice = this.deviceList.find(device => device.device_sn === parentSn);
          if (dockDevice && dockDevice.children && dockDevice.children.device_sn === droneSn) {
            // 更新无人机的mode_code
            this.$set(dockDevice.children, 'mode_code', droneMode);
            console.log('更新无人机mode_code:', {
              dockSn: parentSn,
              droneSn: droneSn,
              oldMode: dockDevice.children.mode_code,
              newMode: droneMode
            });
            // 更新无人机最后更新时间
            this.updateDeviceLastUpdateTime(droneSn);
          } else {
            console.warn('未找到对应的机场或无人机设备:', {
              parentSn,
              droneSn,
              deviceList: this.deviceList
            });
          }
        }
      } catch (error) {
        console.error('处理device_osd消息失败:', error, message);
      }
    },

    // 处理设备离线消息
    handleDeviceOfflineMessage(message) {
      try {
        const { data: messageData } = message;
        if (messageData && messageData.sn) {
          const deviceSn = messageData.sn;
          console.log('收到设备离线消息:', deviceSn);

          // 根据设备SN查找是无人机还是机场
          this.setDeviceOffline(deviceSn);

          this.$message.warning(`设备离线: ${deviceSn}`);
        }
      } catch (error) {
        console.error('处理设备离线消息失败:', error, message);
      }
    },

    // 设置设备为离线状态
    setDeviceOffline(deviceSn) {
      try {
        // 首先检查是否是机场设备
        const dockDevice = this.deviceList.find(device => device.device_sn === deviceSn);
        if (dockDevice) {
          // 是机场设备，设置为离线
          this.$set(dockDevice, 'mode_code', -1);
          console.log('设置机场离线:', deviceSn);
          return;
        }

        // 检查是否是无人机设备
        for (const dock of this.deviceList) {
          if (dock.children && dock.children.device_sn === deviceSn) {
            // 是无人机设备，设置为离线
            this.$set(dock.children, 'mode_code', 14);
            console.log('设置无人机离线:', deviceSn);
            return;
          }
        }

        console.warn('未找到对应的设备:', deviceSn);
      } catch (error) {
        console.error('设置设备离线状态失败:', error);
      }
    },

    // 更新设备最后更新时间
    updateDeviceLastUpdateTime(deviceSn) {
      this.$set(this.deviceLastUpdateTime, deviceSn, Date.now());
    },

    // 启动超时检测
    startTimeoutCheck() {
      if (this.timeoutCheckTimer) {
        clearInterval(this.timeoutCheckTimer);
      }

      this.timeoutCheckTimer = setInterval(() => {
        this.checkDeviceTimeout();
      }, 1000); // 每秒检查一次

      console.log('设备超时检测已启动');
    },

    // 停止超时检测
    stopTimeoutCheck() {
      if (this.timeoutCheckTimer) {
        clearInterval(this.timeoutCheckTimer);
        this.timeoutCheckTimer = null;
        console.log('设备超时检测已停止');
      }
    },

    // 检查设备超时
    checkDeviceTimeout() {
      const currentTime = Date.now();

      try {
        // 检查所有设备的超时状态
        this.deviceList.forEach(dock => {
          // 检查机场超时
          if (dock.device_sn && this.deviceLastUpdateTime[dock.device_sn]) {
            const lastUpdate = this.deviceLastUpdateTime[dock.device_sn];
            if (currentTime - lastUpdate > this.DEVICE_TIMEOUT && dock.mode_code !== -1) {
              console.log('机场设备超时，设置为离线:', dock.device_sn);
              this.$set(dock, 'mode_code', -1);
              this.$message.warning(`机场设备超时离线: ${dock.nickname || dock.device_sn}`);
            }
          }

          // 检查无人机超时
          if (dock.children && dock.children.device_sn && this.deviceLastUpdateTime[dock.children.device_sn]) {
            const lastUpdate = this.deviceLastUpdateTime[dock.children.device_sn];
            if (currentTime - lastUpdate > this.DEVICE_TIMEOUT && dock.children.mode_code !== 14) {
              console.log('无人机设备超时，设置为离线:', dock.children.device_sn);
              this.$set(dock.children, 'mode_code', 14);
              this.$message.warning(`无人机设备超时离线: ${dock.children.nickname || dock.children.device_sn}`);
            }
          }
        });
      } catch (error) {
        console.error('检查设备超时失败:', error);
      }
    },

    // 清理WebSocket资源
    cleanupWebSocket() {
      // 清理WebSocket重连回调
      if (this.reconnectCallback) {
        socketService.removeReconnectCallback(this.reconnectCallback);
      }

      // 断开 WebSocket 连接
      socketService.close();

      // 清理超时检测定时器
      this.stopTimeoutCheck();
    }
  }
};
